package com.lhx.birthday.request.astrolabe;

import com.lhx.birthday.enums.LanguageType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "行星逆顺事件对请求")
public class PlanetRetrogradePairsRequest {

    @ApiModelProperty(value = "行星代号，例如：'2'表示水星", example = "2")
    private String planetCode;
    
    @ApiModelProperty(value = "查询日期，格式 yyyy-MM-dd，不传则查询当前日期", example = "2023-05-15")
    private String date;

    @ApiModelProperty(value = "语言类型 0简体中文 1繁体中文 2英语")
    private LanguageType languageType;
} 